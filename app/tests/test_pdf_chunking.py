import io
import logging
from typing import List, <PERSON><PERSON>

from pypdf import Pdf<PERSON><PERSON><PERSON>, PdfWriter
import pytest
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas


logger = logging.getLogger(__name__)


class PDFChunkingHelper:
    """Helper class for splitting PDF documents into page-based chunks."""

    def __init__(self):
        """Initialize the PDF chunking helper."""
        pass

    def split_pdf_into_chunks(self, pdf_bytes: bytes, pages_per_chunk: int = 2) -> List[Tuple[bytes, int, int]]:
        """
        Split a PDF document into chunks of specified page count.

        Args:
            pdf_bytes: PDF document content as bytes
            pages_per_chunk: Number of pages per chunk (default: 2 for Document Intelligence free tier)

        Returns:
            List of tuples containing (chunk_bytes, start_page, end_page)
            where start_page and end_page are 1-based page numbers

        Raises:
            Exception: If PDF processing fails
        """
        try:
            logger.info(f'Splitting PDF into chunks of {pages_per_chunk} pages each')

            # Read the PDF from bytes
            pdf_reader = PdfReader(io.BytesIO(pdf_bytes))
            total_pages = len(pdf_reader.pages)

            logger.info(f'PDF has {total_pages} total pages')

            if total_pages <= pages_per_chunk:
                # If document has fewer pages than chunk size, return as single chunk
                logger.info(f'Document has {total_pages} pages, returning as single chunk')
                return [(pdf_bytes, 1, total_pages)]

            chunks = []

            # Split into chunks
            for start_page in range(0, total_pages, pages_per_chunk):
                end_page = min(start_page + pages_per_chunk - 1, total_pages - 1)

                # Create a new PDF writer for this chunk
                pdf_writer = PdfWriter()

                # Add pages to the chunk
                for page_num in range(start_page, end_page + 1):
                    pdf_writer.add_page(pdf_reader.pages[page_num])

                # Write chunk to bytes
                chunk_buffer = io.BytesIO()
                pdf_writer.write(chunk_buffer)
                chunk_bytes = chunk_buffer.getvalue()
                chunk_buffer.close()

                # Convert to 1-based page numbers for logging/metadata
                start_page_1based = start_page + 1
                end_page_1based = end_page + 1

                logger.info(
                    f'Created chunk with pages {start_page_1based}-{end_page_1based} ({len(chunk_bytes)} bytes)'
                )
                chunks.append((chunk_bytes, start_page_1based, end_page_1based))

            logger.info(f'Successfully split PDF into {len(chunks)} chunks')
            return chunks

        except Exception as e:
            logger.exception(f'Error splitting PDF into chunks: {str(e)}')
            raise

    def get_pdf_page_count(self, pdf_bytes: bytes) -> int:
        """
        Get the total number of pages in a PDF document.

        Args:
            pdf_bytes: PDF document content as bytes

        Returns:
            Number of pages in the PDF

        Raises:
            Exception: If PDF processing fails
        """
        try:
            pdf_reader = PdfReader(io.BytesIO(pdf_bytes))
            page_count = len(pdf_reader.pages)
            logger.info(f'PDF contains {page_count} pages')
            return page_count
        except Exception as e:
            logger.exception(f'Error getting PDF page count: {str(e)}')
            raise

    def is_pdf_document(self, document_bytes: bytes) -> bool:
        """
        Check if the document is a PDF by examining the file header.

        Args:
            document_bytes: Document content as bytes

        Returns:
            True if the document is a PDF, False otherwise
        """
        try:
            # Check PDF magic number
            if document_bytes.startswith(b'%PDF-'):
                return True
            return False
        except Exception:
            logger.exception('Error checking if document is PDF')
            return False


class TestPDFChunkingHelper:
    """Test cases for PDF chunking functionality."""

    @pytest.fixture
    def pdf_chunking_helper(self):
        """Create a PDFChunkingHelper instance."""
        return PDFChunkingHelper()

    @pytest.fixture
    def sample_pdf_2_pages(self):
        """Create a sample 2-page PDF for testing."""
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)

        # Page 1
        c.drawString(100, 750, 'This is page 1 content')
        c.drawString(100, 700, 'Some sample text on the first page')
        c.showPage()

        # Page 2
        c.drawString(100, 750, 'This is page 2 content')
        c.drawString(100, 700, 'Some sample text on the second page')
        c.showPage()

        c.save()
        return buffer.getvalue()

    @pytest.fixture
    def sample_pdf_5_pages(self):
        """Create a sample 5-page PDF for testing."""
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)

        for page_num in range(1, 6):
            c.drawString(100, 750, f'This is page {page_num} content')
            c.drawString(100, 700, f'Some sample text on page {page_num}')
            c.showPage()

        c.save()
        return buffer.getvalue()

    @pytest.fixture
    def non_pdf_content(self):
        """Create non-PDF content for testing."""
        return b'This is not a PDF file, just plain text content.'

    def test_is_pdf_document_with_pdf(self, pdf_chunking_helper, sample_pdf_2_pages):
        """Test PDF detection with actual PDF content."""
        assert pdf_chunking_helper.is_pdf_document(sample_pdf_2_pages) is True

    def test_is_pdf_document_with_non_pdf(self, pdf_chunking_helper, non_pdf_content):
        """Test PDF detection with non-PDF content."""
        assert pdf_chunking_helper.is_pdf_document(non_pdf_content) is False

    def test_get_pdf_page_count_2_pages(self, pdf_chunking_helper, sample_pdf_2_pages):
        """Test page count detection for 2-page PDF."""
        page_count = pdf_chunking_helper.get_pdf_page_count(sample_pdf_2_pages)
        assert page_count == 2

    def test_get_pdf_page_count_5_pages(self, pdf_chunking_helper, sample_pdf_5_pages):
        """Test page count detection for 5-page PDF."""
        page_count = pdf_chunking_helper.get_pdf_page_count(sample_pdf_5_pages)
        assert page_count == 5

    def test_split_pdf_2_pages_single_chunk(self, pdf_chunking_helper, sample_pdf_2_pages):
        """Test splitting 2-page PDF should return single chunk."""
        chunks = pdf_chunking_helper.split_pdf_into_chunks(sample_pdf_2_pages, pages_per_chunk=2)

        assert len(chunks) == 1
        chunk_bytes, start_page, end_page = chunks[0]
        assert start_page == 1
        assert end_page == 2
        assert isinstance(chunk_bytes, bytes)
        assert len(chunk_bytes) > 0

    def test_split_pdf_5_pages_multiple_chunks(self, pdf_chunking_helper, sample_pdf_5_pages):
        """Test splitting 5-page PDF into 2-page chunks."""
        chunks = pdf_chunking_helper.split_pdf_into_chunks(sample_pdf_5_pages, pages_per_chunk=2)

        # Should create 3 chunks: pages 1-2, 3-4, 5
        assert len(chunks) == 3

        # Check first chunk (pages 1-2)
        chunk1_bytes, start1, end1 = chunks[0]
        assert start1 == 1
        assert end1 == 2
        assert isinstance(chunk1_bytes, bytes)

        # Check second chunk (pages 3-4)
        chunk2_bytes, start2, end2 = chunks[1]
        assert start2 == 3
        assert end2 == 4
        assert isinstance(chunk2_bytes, bytes)

        # Check third chunk (page 5)
        chunk3_bytes, start3, end3 = chunks[2]
        assert start3 == 5
        assert end3 == 5
        assert isinstance(chunk3_bytes, bytes)

    def test_split_pdf_1_page_single_chunk(self, pdf_chunking_helper):
        """Test splitting 1-page PDF should return single chunk."""
        # Create 1-page PDF
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)
        c.drawString(100, 750, 'Single page content')
        c.showPage()
        c.save()
        pdf_bytes = buffer.getvalue()

        chunks = pdf_chunking_helper.split_pdf_into_chunks(pdf_bytes, pages_per_chunk=2)

        assert len(chunks) == 1
        chunk_bytes, start_page, end_page = chunks[0]
        assert start_page == 1
        assert end_page == 1
        assert isinstance(chunk_bytes, bytes)

    def test_split_pdf_custom_chunk_size(self, pdf_chunking_helper, sample_pdf_5_pages):
        """Test splitting PDF with custom chunk size."""
        chunks = pdf_chunking_helper.split_pdf_into_chunks(sample_pdf_5_pages, pages_per_chunk=3)

        # Should create 2 chunks: pages 1-3, 4-5
        assert len(chunks) == 2

        # Check first chunk (pages 1-3)
        chunk1_bytes, start1, end1 = chunks[0]
        assert start1 == 1
        assert end1 == 3

        # Check second chunk (pages 4-5)
        chunk2_bytes, start2, end2 = chunks[1]
        assert start2 == 4
        assert end2 == 5

    def test_get_pdf_page_count_with_non_pdf_raises_exception(self, pdf_chunking_helper, non_pdf_content):
        """Test that getting page count of non-PDF content raises exception."""
        with pytest.raises(Exception):
            pdf_chunking_helper.get_pdf_page_count(non_pdf_content)

    def test_split_pdf_with_non_pdf_raises_exception(self, pdf_chunking_helper, non_pdf_content):
        """Test that splitting non-PDF content raises exception."""
        with pytest.raises(Exception):
            pdf_chunking_helper.split_pdf_into_chunks(non_pdf_content)
