"""
Tests for client creation flow when no existing client is found.
"""

from unittest.mock import AsyncMock, patch

from fastapi import status
from httpx import AsyncClient

from constants.extracted_data import ConversationState
from constants.message import (
    CLIENT_CREATION_CONFIRMED,
    CLIENT_NOT_FOUND_PROMPT,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    SuggestedUserPrompt,
)
from constants.operation_ids import operation_ids
from schemas import ClientSearchResponse, ConfirmedData
from schemas.quals_clients import ClientComprehensive, ClientCreateResponse


class TestClientCreationFlow:
    """Test client creation flow for new clients."""

    async def test_client_creation_confirmation_flow(
        self,
        auth_header,
        async_client: AsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test the complete flow: no client found -> ask to create -> user confirms -> client created.
        """
        # Step 1: User provides unknown client name
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.conversation.ConversationRepository.update_state') as mock_update_state,
            patch(
                'repositories.conversation.ConversationRepository.update_confirmed_data_and_state'
            ) as mock_update_confirmed_and_state,
            patch(
                'services.message_processor.ConversationMessageProcessor._detect_client_name_from_message'
            ) as mock_detect,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks for first message
            mock_conversation = type('MockConversation', (), {'id': test_conversation_id, 'State': None})
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock client name detection
            mock_detect.return_value = 'NewTech Solutions'

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify first response - should ask if user wants to add as new client
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        assert 'system' in data
        system_data = data['system']
        assert CLIENT_NOT_FOUND_PROMPT.format(client_name='NewTech Solutions') in system_data['content']

        # Verify conversation state was updated and proposed client name was stored
        mock_update_state.assert_called_once_with(test_conversation_id, ConversationState.COLLECTING_CLIENT_NAME)
        mock_update_confirmed_and_state.assert_called_once()

        # Step 2: User confirms they want to add the new client
        confirmation_message = SuggestedUserPrompt.YES_ADD_NEW_CLIENT.value

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation2,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data2,
            patch('repositories.quals_clients.QualsClientsRepository.create_client') as mock_create_client,
            patch(
                'services.extracted_data.service.ExtractedDataService.update_confirmed_data'
            ) as mock_update_confirmed,
        ):
            # Set up mocks for confirmation message
            mock_get_conversation2.return_value = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': ConversationState.COLLECTING_CLIENT_NAME}
            )

            # Mock confirmed data with proposed client name
            confirmed_data_with_proposed = ConfirmedData(proposed_client_name='NewTech Solutions')
            mock_get_confirmed_data2.return_value = confirmed_data_with_proposed

            # Mock successful client creation
            mock_client = ClientComprehensive(
                id='newtech-001',
                name='NewTech Solutions',
                description='Technology consulting company',
                primaryLocalIndustry='Technology',
                primaryGlobalIndustry='Information Technology',
            )
            mock_create_client.return_value = ClientCreateResponse(
                client=mock_client, success=True, message='Client created successfully'
            )

            confirmation_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': confirmation_message,
            }

            confirmation_response = await async_client.post(message_url, headers=auth_header, data=confirmation_data)

        # Verify confirmation response - should confirm client creation
        assert confirmation_response.status_code == status.HTTP_201_CREATED
        confirmation_data = confirmation_response.json()

        assert 'system' in confirmation_data
        system_data = confirmation_data['system']
        assert CLIENT_CREATION_CONFIRMED.format(client_name='NewTech Solutions') in system_data['content']

        # Verify client was created and confirmed data was updated
        mock_create_client.assert_called_once()
        mock_update_confirmed.assert_called_once_with(
            conversation_id=test_conversation_id,
            field_name='client_name',
            field_value='NewTech Solutions',
            state=ConversationState.COLLECTING_COUNTRY,
        )

    async def test_client_creation_alternative_name_flow(
        self,
        auth_header,
        async_client: AsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test flow where user provides alternative client name instead of confirming.
        """
        # Setup: User has been asked to confirm 'OldTech Corp' but provides different name
        alternative_message = 'No, the client is actually ModernTech Inc'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.conversation.ConversationRepository.update_state') as mock_update_state,
            patch(
                'services.message_processor.ConversationMessageProcessor._detect_client_name_from_message'
            ) as mock_detect,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks
            mock_get_conversation.return_value = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': ConversationState.COLLECTING_CLIENT_NAME}
            )

            # Mock confirmed data with proposed client name (from previous interaction)
            confirmed_data_with_proposed = ConfirmedData(proposed_client_name='OldTech Corp')
            mock_get_confirmed_data.return_value = confirmed_data_with_proposed

            # Mock detection of new client name
            mock_detect.return_value = 'ModernTech Inc'

            # Mock search for new client name (also no results)
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': alternative_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response - should ask about the new client name
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        assert 'system' in data
        system_data = data['system']
        assert CLIENT_NOT_FOUND_PROMPT.format(client_name='ModernTech Inc') in system_data['content']

        # Verify the proposed client name was cleared and new search was performed
        mock_search.assert_called_once()
        mock_update_state.assert_called_once_with(test_conversation_id, ConversationState.COLLECTING_CLIENT_NAME)
