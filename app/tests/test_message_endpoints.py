import copy
from io import By<PERSON><PERSON>
import json
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from deepdiff import DeepD<PERSON>
from fastapi import status
from httpx import AsyncClient
import pytest

from config import settings
from constants.extracted_data import ConversationState, MissingDataStatus
from constants.message import (
    BRIEF_DESCRIPTION_REPLY,
    CLIENT_NAME_MULTIPLE_OPTIONS,
    EXAMPLE_REPLY,
    UNDEFINED_REPLY,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
)
from constants.operation_ids import operation_ids
from schemas import ConfirmedData, MessageValidator, MissingDataResponse


EMPTY_MESSAGE = ''


@pytest.mark.parametrize(
    'parametrized_data',
    [
        {
            'mocked_intent': ConversationMessageIntention.UNDEFINED,
            'expected_system_reply': UNDEFINED_REPLY,
            'mocked_ldmf_countries': [],
        },
        {
            'mocked_intent': ConversationMessageIntention.UNCERTAINTY,
            'expected_system_reply': BRIEF_DESCRIPTION_REPLY,
            'mocked_ldmf_countries': [],
        },
        {
            'mocked_intent': ConversationMessageIntention.EXAMPLE,
            'expected_system_reply': EXAMPLE_REPLY,
            'mocked_ldmf_countries': [],
        },
    ],
)
async def test_create_message_success(
    parametrized_data,
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = 'Test message content'
    mocked_intent = parametrized_data['mocked_intent']
    expected_system_reply = parametrized_data['expected_system_reply']
    mocked_ldmf_countries = parametrized_data['mocked_ldmf_countries']

    with (
        patch(
            'services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent
        ) as mocked_intent,
        patch(
            'repositories.ldmf_countries.LDMFCountriesRepository.get_ldmf_countries',
            new_callable=AsyncMock,
            return_value=mocked_ldmf_countries,
        ) as mocked_ldmf_countries,
    ):
        conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)

        conversation_id = conversation_response.json()['conversation']['id']
        message_data = {
            'conversation_id': conversation_id,
            'role': MessageRole.USER,
            'message_type': MessageType.TEXT,
            'content': user_message,
        }

        message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

    mocked_intent.assert_called_once()

    data = message_response.json()

    assert message_response.status_code == status.HTTP_201_CREATED

    # Test that the response contains a user message with the expected properties
    assert data['user']['conversation_id'] == conversation_id
    assert data['user']['content'] == 'Test message content'
    assert data['user']['role'] == str(MessageRole.USER)
    assert data['user']['type'] == str(MessageType.TEXT)
    assert 'created_at' in data['user']
    assert 'id' in data['user']
    assert 'selected_option' in data['user']

    # Test that the response contains a system message
    assert 'system' in data

    # Test that the response doesn't contain files
    assert data['files'] is None
    assert data['system']['content'] == expected_system_reply


async def test_create_message_dash_task(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    activity_id = 100001
    user_message = 'Test message content'

    mocked_intent_value = ConversationMessageIntention.UNDEFINED
    with patch(
        'services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent_value
    ):
        conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
        conversation_id = conversation_response.json()['conversation']['id']
        message_data = {
            'conversation_id': conversation_id,
            'selected_option': json.dumps(
                {
                    'client_name': 'Client#1',
                    'activity_id': activity_id,
                    'type': OptionType.KX_DASH_TASK.value,
                    'engagement_code': '',
                }
            ),
            'content': user_message,
        }
        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        mock_system_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Mock system response for dash task selection',
        )

        headers = auth_header.copy()
        with (
            patch('services.extracted_data.service.ExtractedDataService.update', return_value=None),
            patch('services.kx_dash.service.KXDashService.on_select', return_value=mock_system_message),
        ):
            message_response = await async_client.post(
                message_url,
                headers=headers,
                data=message_data,
            )

    data = message_response.json()
    expected_message = {
        'id': data['user']['id'],
        'conversation_id': conversation_id,
        'role': str(MessageRole.USER),
        'type': str(MessageType.TEXT),
        'content': user_message,
        'selected_option': json.loads(message_data['selected_option']),
        'created_at': data['user']['created_at'],
    }

    assert data['user'] == expected_message, DeepDiff(expected_message, data['user'], ignore_order=True)
    assert data['files'] is None


async def test_create_message_unable_to_get_internal_id_(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test message creation fails properly"""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)

    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)

    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    conversation_id = conversation_response.json()['conversation']['id']
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message content',
    }
    with (
        patch('repositories.conversation_message.ConversationRepository.get_internal_id') as get_internal_id_mock,
    ):
        get_internal_id_mock.new_callable = AsyncMock
        get_internal_id_mock.return_value = None

        message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

    assert message_response.status_code == status.HTTP_404_NOT_FOUND


async def test_create_message_nonexistent_conversation(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
):
    nonexistent_id = str(uuid4())
    message_data = {
        'conversation_id': nonexistent_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message content',
    }
    url = url_resolver.reverse('create_message')

    response = await async_client.post(url, headers=auth_header, data=message_data)

    expected = {'detail': f'Conversation with ID {nonexistent_id} not found'}

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == expected


@pytest.mark.parametrize(
    'invalid_data',
    (
        {},
        {
            'conversation_id': 'not-a-uuid',
            'content': '123',
        },
    ),
)
async def test_create_message_validation_errors(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    invalid_data,
):
    url = url_resolver.reverse('create_message')

    response = await async_client.post(url, headers=auth_header, data=invalid_data)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert 'detail' in response.json()


async def test_get_message_success(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = 'Test message with files'

    mocked_intent_value = ConversationMessageIntention.UNDEFINED
    with patch(
        'services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent_value
    ) as mocked_intent:
        conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
        conversation_id = conversation_response.json()['conversation']['id']
        message_data = {
            'conversation_id': conversation_id,
            'role': MessageRole.USER,
            'message_type': MessageType.TEXT,
            'content': user_message,
        }
        message_create_url = url_resolver.reverse(operation_ids.message.CREATE)

        message_create_response = await async_client.post(message_create_url, headers=auth_header, data=message_data)

        message_id = message_create_response.json()['user']['id']
        message_get_url = url_resolver.reverse(operation_ids.message.GET, message_id=message_id)

        message_get_response = await async_client.get(message_get_url, headers=auth_header)

    mocked_intent.assert_called_once()

    data = message_get_response.json()
    expected_response = {
        'id': message_id,
        'conversation_id': conversation_id,
        'role': str(MessageRole.USER),
        'type': str(MessageType.TEXT),
        'content': user_message,
        'selected_option': None,
        'created_at': data['created_at'],
    }

    assert message_get_response.status_code == status.HTTP_200_OK
    assert data == expected_response, DeepDiff(expected_response, data, ignore_order=True)


async def test_get_message_not_found(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
):
    message_id = uuid4()
    url = url_resolver.reverse(operation_ids.message.GET, message_id=message_id)

    response = await async_client.get(url, headers=auth_header)

    expected = {'detail': f'Message with ID {message_id} not found'}

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == expected


async def test_get_message_invalid_uuid(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
):
    url = url_resolver.reverse(operation_ids.message.GET, message_id='not-a-uuid')

    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert 'detail' in response.json()


async def test_get_message_wrong_user_id(
    auth_mock,
    auth_header,
    decoded_jwt_token,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)

    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)

    conversation_id = conversation_response.json()['conversation']['id']
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message content',
    }
    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    message_create_response = await async_client.post(message_create_url, headers=auth_header, data=message_data)

    message_id = message_create_response.json()['user']['id']

    with (
        patch('middleware.auth.AzureADAuthorizerMiddleware._decode_token') as decode_token_mock,
    ):
        decode_token_mock.new_callable = AsyncMock
        decode_token_mock.return_value = copy.deepcopy(decoded_jwt_token)
        decode_token_mock.return_value['oid'] = str(uuid4())

        message_get_url = url_resolver.reverse(operation_ids.message.GET, message_id=message_id)
        message_get_response = await async_client.get(message_get_url, headers=auth_header)

    assert message_get_response.status_code == status.HTTP_404_NOT_FOUND


async def test_create_message_with_only_files(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = EMPTY_MESSAGE
    mocked_intent_value = None
    response_content = EMPTY_MESSAGE

    # Create conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data with empty content
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': user_message,
    }

    # Prepare files
    mime_type = 'application/pdf'
    test_files = [
        ('files', ('test_file.pdf', BytesIO(b'Test file content'), mime_type)),
    ]

    # Send request with only files
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    with patch(
        'services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent_value
    ) as mocked_intent:
        response = await async_client.post(
            message_url,
            headers=auth_header,
            data=message_data,
            files=test_files,
        )
        mocked_intent.assert_not_called()

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED, response.json()
    data = response.json()

    # Verify user message was created with FILE type and filename as content
    assert data['user']['conversation_id'] == conversation_id
    assert data['user']['content'] == user_message
    assert data['user']['role'] == str(MessageRole.USER)
    assert data['user']['type'] == str(MessageType.FILE)
    assert data['system']['content'] == response_content

    # Verify files were created
    assert data['files'] is not None
    assert len(data['files']) == 1
    assert data['files'][0]['file_name'] == 'test_file.pdf'
    assert data['files'][0]['message_id'] == data['user']['id']


async def test_create_message_with_multiple_files_no_content(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = EMPTY_MESSAGE
    mocked_intent_value = ConversationMessageIntention.UNCERTAINTY
    response_content = EMPTY_MESSAGE

    # Create conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data with empty content
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': user_message,
    }

    # Prepare multiple files
    mime_type = 'application/pdf'
    test_files = [
        ('files', ('file1.pdf', BytesIO(b'Test file content 1'), mime_type)),
        ('files', ('file2.pdf', BytesIO(b'Test file content 2'), mime_type)),
    ]

    # Send request with only files
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    with patch(
        'services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent_value
    ) as mocked_intent:
        response = await async_client.post(
            message_url,
            headers=auth_header,
            data=message_data,
            files=test_files,
        )
        mocked_intent.assert_not_called()

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()

    # Verify user message was created with FILE type and first filename as content
    assert data['user']['conversation_id'] == conversation_id
    assert data['user']['content'] == user_message
    assert data['user']['role'] == str(MessageRole.USER)
    assert data['user']['type'] == str(MessageType.FILE)
    assert 'selected_option' in data['user']

    assert data['system']['content'] == response_content

    # Verify files were created
    assert data['files'] is not None
    assert len(data['files']) == 2
    assert any(file['file_name'] == 'file1.pdf' for file in data['files'])
    assert any(file['file_name'] == 'file2.pdf' for file in data['files'])


async def test_create_message_with_multiple_client_names_requires_confirmation(
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    test_conversation_id,
):
    """
    Test that when aggregated data contains multiple client names but user hasn't confirmed
    their selection yet, the system message includes client name options for selection.

    This test uses controlled mocking to test the business logic while avoiding complex fixtures.
    """
    user_message = 'I need help with my qual'

    # Mock intent classification, database operations, and external API calls
    with (
        patch(
            'services.conversation_message.ConversationMessageProcessor._get_intent',
            new_callable=AsyncMock,
            return_value=ConversationMessageIntention.EXTRACTION,
        ) as mocked_get_intent,
        patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
        patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
        patch('repositories.conversation.ConversationRepository.update_state') as mock_update_state,
        patch(
            'services.extracted_data.service.ExtractedDataService.get_missing_required_data_prompts'
        ) as mock_get_missing_data,
    ):
        # Set up mock conversation (exists)
        mock_conversation = type('MockConversation', (), {'id': test_conversation_id, 'State': None})
        mock_get_conversation.return_value = mock_conversation
        mock_get_confirmed_data.return_value = ConfirmedData()

        mock_missing_data_response = MissingDataResponse(
            status=MissingDataStatus.MISSING_DATA,
            message=CLIENT_NAME_MULTIPLE_OPTIONS,
            next_expected_field='client_name',
            missing_fields=['client_name'],
            conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
            options=['Document Client', 'Client B'],
        )
        mock_get_missing_data.return_value = mock_missing_data_response

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'role': MessageRole.USER,
            'message_type': MessageType.TEXT,
            'content': user_message,
        }

        message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

    # Verify the intent classification was called
    mocked_get_intent.assert_called_once()

    # Verify database operations were called
    mock_get_conversation.assert_called_once_with(test_conversation_id)
    mock_get_confirmed_data.assert_called_with(test_conversation_id)
    mock_get_missing_data.assert_called_once()
    mock_update_state.assert_called_once_with(test_conversation_id, ConversationState.COLLECTING_CLIENT_NAME)

    data = message_response.json()
    expected_message = {
        'id': data['user']['id'],
        'conversation_id': str(test_conversation_id),
        'role': str(MessageRole.USER),
        'type': str(MessageType.TEXT),
        'content': user_message,
        'selected_option': None,
        'created_at': data['user']['created_at'],
    }

    assert message_response.status_code == status.HTTP_201_CREATED
    assert data['user'] == expected_message
    assert data['files'] is None

    # Verify that the system response includes client name options
    assert 'system' in data
    system_data = data['system']

    # The system should ask for client name confirmation and provide options
    assert 'content' in system_data
    assert 'options' in system_data

    # Check that the system message is about client name selection
    assert system_data['content'] == CLIENT_NAME_MULTIPLE_OPTIONS

    # Verify that options are provided as ClientNameOption objects
    assert len(system_data['options']) == 2
    # Options should be serialized as objects with type and client_name
    for option in system_data['options']:
        assert 'type' in option
        assert 'client_name' in option
        assert option['type'] == 'client_name'
        assert option['client_name'] in ['Document Client', 'Client B']


async def test_create_message_fail_on_file_size(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = ''

    mocked_intent_value = ConversationMessageIntention.UNDEFINED
    with patch(
        'services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent_value
    ):
        # Create conversation
        conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
        conversation_id = conversation_response.json()['conversation']['id']

        # Prepare message data with empty content
        message_data = {
            'conversation_id': conversation_id,
            'role': MessageRole.USER,
            'message_type': MessageType.TEXT,
            'content': user_message,
        }

        # Prepare files
        mime_type = 'application/pdf'
        test_files = [
            ('files', ('test_file.pdf', BytesIO(b'Test file content'), mime_type)),
        ]

        # Send request with only files
        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        with patch(
            'repositories.document_db.DocumentDbRepository.get_total_size_by_conversation_id',
            return_value=settings.document_storage.max_conversation_size + 1,
        ):
            response = await async_client.post(
                message_url,
                headers=auth_header,
                data=message_data,
                files=test_files,
            )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()['detail'].startswith('Total document size')
