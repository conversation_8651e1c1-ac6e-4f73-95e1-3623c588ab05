from unittest.mock import AsyncMock

from fastapi import FastAPI, status
from httpx import ASGITransport, AsyncClient
import pytest
from starlette.types import ASGIApp

from constants.operation_ids import operation_ids
from dependencies.services import get_kx_dash_service
from exceptions import EntityNotFoundError


@pytest.fixture
def mock_kx_dash_service(async_client: AsyncClient):
    service = AsyncMock()
    transport = async_client._transport
    if isinstance(transport, ASGITransport):
        app: ASGIApp = transport.app
        if not isinstance(app, FastAPI):
            raise RuntimeError('Expected FastAPI app')
        original_factory = app.dependency_overrides.get(get_kx_dash_service)
        app.dependency_overrides[get_kx_dash_service] = lambda: service
        yield service
        if original_factory:
            app.dependency_overrides[get_kx_dash_service] = original_factory
        else:
            del app.dependency_overrides[get_kx_dash_service]
    else:
        raise RuntimeError('Expected ASGITransport')


async def test_list_activities_success(
    mock_kx_dash_service,
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    mock_activities_response,
    mock_activity,
):
    # Mock the service to return activities
    mock_kx_dash_service.list.return_value = [mock_activity]

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_200_OK
    mock_kx_dash_service.list.assert_called_once()

    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 1

    activity = data[0]
    assert activity['activity_id'] == mock_activity['activityId']
    assert activity['activity_name'] == mock_activity['activityName']
    assert activity['client_name'] == mock_activity['clientName']
    assert activity['engagement_code'] == mock_activity['engagementCode']
    assert activity['due_date'] == mock_activity['dueDate']


async def test_list_activities_missing_query(
    mock_kx_dash_service, auth_mock, auth_header, async_client: AsyncClient, url_resolver
):
    # Mock service to raise an error when no activities are found
    mock_kx_dash_service.list.side_effect = EntityNotFoundError('Activities', 'No activities found')

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.list.assert_called_once()
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to list activities'}


async def test_list_activities_server_error(
    mock_kx_dash_service, auth_mock, auth_header, async_client: AsyncClient, url_resolver
):
    # Mock service to raise an error
    mock_kx_dash_service.list.side_effect = Exception('Server error')

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.list.assert_called_once()
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to list activities'}


async def test_get_activity_success(
    mock_kx_dash_service, auth_mock, auth_header, async_client: AsyncClient, url_resolver, mock_activity
):
    # Mock service to return activity
    mock_kx_dash_service.get.return_value = mock_activity

    activity_id = 100001
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.get.assert_called_once_with(activity_id)
    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert data['activity_id'] == mock_activity['activityId']
    assert data['activity_name'] == mock_activity['activityName']
    assert data['client_name'] == mock_activity['clientName']
    assert data['engagement_code'] == mock_activity['engagementCode']
    assert data['member_firm'] == mock_activity['memberFirm']
    assert data['country'] == mock_activity['country']
    assert data['due_date'] == mock_activity['dueDate']


async def test_get_activity_not_found(
    mock_kx_dash_service, auth_mock, auth_header, async_client: AsyncClient, url_resolver
):
    activity_id = 999999
    # Mock service to raise EntityNotFoundError
    mock_kx_dash_service.get.side_effect = EntityNotFoundError('Activity', str(activity_id))

    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.get.assert_called_once_with(activity_id)
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert 'not found' in response.json()['detail']


async def test_get_activity_server_error(
    mock_kx_dash_service, auth_mock, auth_header, async_client: AsyncClient, url_resolver
):
    activity_id = 100001
    # Mock service to raise an error
    mock_kx_dash_service.get.side_effect = Exception('Server error')

    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.get.assert_called_once_with(activity_id)
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to retrieve activity'}


async def test_get_activity_invalid_id(auth_mock, auth_header, async_client: AsyncClient, url_resolver):
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id='not-an-integer')

    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
