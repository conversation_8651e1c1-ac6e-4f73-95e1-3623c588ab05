"""
Tests for client name detection and confirmation flow.
"""

from datetime import datetime
from unittest.mock import AsyncMock, patch
from uuid import UUI<PERSON>

from fastapi import status
from httpx import AsyncClient
from pydantic import ValidationError
import pytest

from constants.extracted_data import ConversationState
from constants.message import (
    CLIENT_NAME_CONFIRMED,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
)
from constants.operation_ids import operation_ids
from schemas import ClientSearchItem, ClientSearchResponse, ConfirmedData, UserMessageSerializer
from services.message_processor import ConversationMessageProcessor


class TestClientNameDetection:
    """Test client name detection and confirmation flow."""

    async def test_single_client_match_auto_confirmation(
        self,
        auth_header,
        async_client: AsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test that when exactly ONE client match is found, it's automatically confirmed.
        """
        user_message = 'I need help with my qual for Microsoft'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch(
                'services.message_processor.ConversationMessageProcessor._detect_client_name_from_message'
            ) as mock_detect,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch(
                'services.extracted_data.service.ExtractedDataService.update_confirmed_data'
            ) as mock_update_confirmed,
        ):
            # Set up mocks
            mock_conversation = type('MockConversation', (), {'id': test_conversation_id, 'State': None})
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock client name detection
            mock_detect.return_value = 'Microsoft'

            # Mock single client search result
            mock_search.return_value = ClientSearchResponse(
                clients=[
                    ClientSearchItem(id='ms-001', name='Microsoft Corporation', qualsCount=50, clientConfidentiality=1)
                ],
                total_count=1,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        # Should auto-confirm and show confirmation message
        assert 'system' in data
        system_data = data['system']
        assert CLIENT_NAME_CONFIRMED.format(client_name='Microsoft Corporation') in system_data['content']

        # Verify the client name was updated
        mock_update_confirmed.assert_called_once_with(
            conversation_id=test_conversation_id,
            field_name='client_name',
            field_value='Microsoft Corporation',
            state=ConversationState.COLLECTING_COUNTRY,
        )

    async def test_single_client_match_requires_confirmation(
        self,
        auth_header,
        async_client: AsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test that when multiple matches are found, user confirmation is required.
        """
        user_message = 'I need help with my qual for Apple'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.conversation.ConversationRepository.update_state') as mock_update_state,
            patch(
                'services.message_processor.ConversationMessageProcessor._detect_client_name_from_message'
            ) as mock_detect,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks
            mock_conversation = type('MockConversation', (), {'id': test_conversation_id, 'State': None})
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock client name detection
            mock_detect.return_value = 'Apple'

            # Mock multiple client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[
                    ClientSearchItem(id='apple-001', name='Apple Inc.', qualsCount=30, clientConfidentiality=1),
                    ClientSearchItem(id='apple-002', name='Apple Corp', qualsCount=15, clientConfidentiality=1),
                ],
                total_count=2,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        # Should ask for confirmation
        assert 'system' in data
        system_data = data['system']
        assert CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name='Apple') in system_data['content']

        # Verify conversation state was updated to collecting client name
        mock_update_state.assert_called_once_with(test_conversation_id, ConversationState.COLLECTING_CLIENT_NAME)

    async def test_no_client_match_requires_confirmation(
        self,
        auth_header,
        async_client: AsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test that when no matches are found, user confirmation is still required.
        """
        user_message = 'I need help with my qual for Unknown Company'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.conversation.ConversationRepository.update_state') as mock_update_state,
            patch(
                'services.message_processor.ConversationMessageProcessor._detect_client_name_from_message'
            ) as mock_detect,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks
            mock_conversation = type('MockConversation', (), {'id': test_conversation_id, 'State': None})
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock client name detection
            mock_detect.return_value = 'Unknown Company'

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        # Should ask if user wants to add as new client
        assert 'system' in data
        system_data = data['system']
        assert CLIENT_NOT_FOUND_PROMPT.format(client_name='Unknown Company') in system_data['content']

        # Verify conversation state was updated to collecting client name
        mock_update_state.assert_called_once_with(test_conversation_id, ConversationState.COLLECTING_CLIENT_NAME)


class TestClientNameDetectionPatterns:
    """Test client name detection patterns."""

    @pytest.mark.parametrize(
        'message,expected',
        [
            ('client is Microsoft', 'Microsoft'),
            ('working with Apple Inc', 'Apple'),
            ('for Google company', 'Google'),
            ('at Amazon company', 'Amazon'),
            ('client name is Tesla', 'Tesla'),
            ('client: Netflix', 'Netflix'),
            ('I need help with my qual', None),  # No client name
            ('working on something', None),  # No client name
        ],
    )
    async def test_client_name_detection_patterns(self, message, expected, test_conversation_id):
        """Test various client name detection patterns."""
        user_serial = UserMessageSerializer(
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=message,
            created_at=datetime.now(),
            id=UUID('00000000-0000-0000-0000-000000000001'),
            selected_option=None,
        )

        # Create a mock processor to test the detection method
        processor = ConversationMessageProcessor(
            conversation_id=test_conversation_id,
            user_message=user_serial,
            files=None,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
        )

        # Test the detection method directly
        result = await processor._detect_client_name_from_message(message)
        assert result == expected

    async def test_user_message_is_none(self, test_conversation_id):
        with pytest.raises(ValidationError):
            user_serial = UserMessageSerializer(
                conversation_id=test_conversation_id,
                role=MessageRole.USER,
                type=MessageType.TEXT,
                content=None,  # type: ignore
                created_at=datetime.now(),
                id=UUID('00000000-0000-0000-0000-000000000001'),
                selected_option=None,
            )
            # Create a mock processor to test the detection method
            processor = ConversationMessageProcessor(
                conversation_id=test_conversation_id,
                user_message=user_serial,  # type: ignore
                files=None,
                intent_classifier_service=AsyncMock(),
                extracted_data_service=AsyncMock(),
                conversation_repository=AsyncMock(),
                date_validator_service=AsyncMock(),
                document_service=AsyncMock(),
            )
            _ = await processor.run()
