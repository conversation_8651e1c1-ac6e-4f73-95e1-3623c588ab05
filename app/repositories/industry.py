import logging
from typing import Any

from httpx import AsyncClient

from config import settings
from core.repositories import BaseHttpRepository


__all__ = ['IndustryRepository']

logger = logging.getLogger(__name__)


class IndustryRepository(BaseHttpRepository):
    """Repository for Industry API operations."""

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the Industry Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
        """
        super().__init__(http_client)
        self._base_path = settings.industries_api.base_url

    async def list(self, token: str) -> list[dict[str, Any]]:
        """
        List all industries.

        Returns:
            list[dict[str, Any]]: A list of industries
        """
        url = f'{self._base_path}/industries-all'
        headers = {'Authorization': f'Bearer {token}'}
        try:
            return await self._execute_request('get', url, headers=headers)
        except Exception as e:
            logger.error('Error listing industries: %s', e)
            raise e
