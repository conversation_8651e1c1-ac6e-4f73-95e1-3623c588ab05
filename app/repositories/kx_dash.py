from datetime import datetime
from typing import Any, Dict, List

from httpx import AsyncClient

from config import settings
from constants.environment import Environment
from core.repositories import BaseHttpRepository
from exceptions.kx_dash import KXDashDateParsingError


__all__ = ['KXDashRepository']


class KXDashRepository(BaseHttpRepository):
    """Repository for KX Dash API operations."""

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the KX Dash Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
        """
        super().__init__(http_client)
        self._base_path = settings.kx_dash_api.base_url

    @staticmethod
    def _mock_list_tasks() -> List[Dict[str, Any]]:
        return [
            {
                'activityId': 100001,
                'activityName': 'Quality Review - Annual Audit',
                'clientName': 'TechCorp Industries',
                'memberFirm': 'US',
                'country': 'United States',
                'globalBusiness': 'Audit & Assurance',
                'globalBusinessServiceArea': 'Audit',
                'globalBusinessServiceLine': 'External Audit',
                'globalIndustry': 'Technology, Media & Telecommunications',
                'globalIndustrySector': 'Software & Computer Services',
                'engagementCode': 'ENG-100001',
                'globalLCSPEmails': ['<EMAIL>', '<EMAIL>'],
                'engagementLepEmails': ['<EMAIL>'],
                'engagementManagerEmails': ['<EMAIL>', '<EMAIL>'],
                'activityOwnerEmails': ['<EMAIL>'],
                'engagementStartDate': '2024-01-15',
                'engagementEndDate': '2024-12-31',
                'dueDate': '2024-04-30',
            },
            {
                'activityId': 100002,
                'activityName': 'Risk Assessment - Financial Services Compliance',
                'clientName': 'Global Financial Solutions Ltd',
                'memberFirm': 'UK',
                'country': 'United Kingdom',
                'globalBusiness': 'Risk Advisory',
                'globalBusinessServiceArea': 'Risk & Financial Advisory',
                'globalBusinessServiceLine': 'Regulatory & Legal Support',
                'globalIndustry': 'Financial Services',
                'globalIndustrySector': 'Investment Banking',
                'engagementCode': 'ENG-100002',
                'globalLCSPEmails': ['<EMAIL>', '<EMAIL>'],
                'engagementLepEmails': ['<EMAIL>', '<EMAIL>'],
                'engagementManagerEmails': ['<EMAIL>'],
                'activityOwnerEmails': ['<EMAIL>', '<EMAIL>'],
                'engagementStartDate': '2024-03-01',
                'engagementEndDate': '2024-09-30',
                'dueDate': '2024-06-15',
            },
        ]

    async def list(self) -> List[Dict[str, Any]]:
        """
        Get list of activities from KX Quals API.

        Returns:
            list[dict[str, Any]]: List of activities in dash format
        """
        if settings.environment == Environment.LOCAL:
            return self._mock_list_tasks()

        url = f'{self._base_path}/getMyFilteredTasks'
        response = await self._execute_request('GET', url)
        parsed_response = self._parse_kx_dash_response(response)

        return parsed_response

    async def get(self, activity_id: int) -> Dict[str, Any] | None:
        """
        Get a specific activity from KX Quals API.

        Args:
            activity_id: The ID of the activity to retrieve

        Returns:
            dict[str, Any] | None: Activity data in dash format or None if not found
        """
        url = f'{self._base_path}/getActivityDataById'
        response = await self._execute_request('GET', url, params={'activityId': activity_id})

        return response if response else None

    @staticmethod
    def _parse_date_string(date_string: str | None) -> str | None:
        """
        Parse date string from API response and convert to date format.

        Args:
            date_string: Date string in format '2025-04-19T00:00:00Z' or '2025-04-19'

        Returns:
            Date string in format '2025-04-19' or None
        """
        if not date_string:
            return None

        # Parse datetime string and extract date part
        try:
            # Handle Z timezone suffix
            clean_date_string = date_string.replace('Z', '+00:00')
            parsed_datetime = datetime.fromisoformat(clean_date_string)
            return parsed_datetime.date().isoformat()
        except Exception:
            raise KXDashDateParsingError(date_string)

    def _parse_kx_dash_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Parse KX Quals API response and convert it to dash task format.

        Args:
            response: Raw response from KX Quals API

        Returns:
            list[dict[str, Any]]: List of tasks in dash format
        """
        tasks = []

        for item in response.get('data', []):
            # Extract owner information
            owners = item.get('owner', {}).get('value', [])
            owner_emails = [owner.get('email') for owner in owners if owner.get('email')]

            # Extract engagement information
            engagement = item.get('engagement', {})

            task = {
                'activityId': item.get('id', {}).get('value'),
                'activityName': item.get('activityName', {}).get('value'),
                'clientName': item.get('clientNameLocal', {}).get('value'),
                'memberFirm': item.get('memberFirm', {}).get('value'),
                'country': item.get('country', {}).get('value'),
                'globalBusiness': item.get('globalBusiness', {}).get('value'),
                'globalBusinessServiceArea': item.get('globalBusinessServiceArea', {}).get('value'),
                'globalBusinessServiceLine': item.get('globalBusinessServiceLine', {}).get('value'),
                'globalIndustry': item.get('globalIndustry', {}).get('value'),
                'globalIndustrySector': item.get('globalIndustrySector', {}).get('value'),
                'engagementCode': engagement.get('code', {}).get('value'),
                'globalLCSPEmails': item.get('globalLCSPEmails', {}).get('value', []),
                'engagementLepEmails': engagement.get('lep', {}).get('value', []),
                'engagementManagerEmails': engagement.get('manager', {}).get('value', []),
                'activityOwnerEmails': owner_emails,
                'engagementStartDate': self._parse_date_string(engagement.get('startDate', {}).get('value')),
                'engagementEndDate': self._parse_date_string(engagement.get('endDate', {}).get('value')),
                'dueDate': self._parse_date_string(item.get('dueDate', {}).get('value')),
            }
            tasks.append(task)

        return tasks
