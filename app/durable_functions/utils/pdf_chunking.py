import io
import logging
from typing import List, Tuple

from pypdf import Pdf<PERSON><PERSON><PERSON>, PdfWriter


logger = logging.getLogger(__name__)


class PDFChunkingHelper:
    """Helper class for splitting PDF documents into page-based chunks."""

    def __init__(self):
        """Initialize the PDF chunking helper."""
        pass

    def split_pdf_into_chunks(self, pdf_bytes: bytes, pages_per_chunk: int = 2) -> List[Tuple[bytes, int, int]]:
        """
        Split a PDF document into chunks of specified page count.

        Args:
            pdf_bytes: PDF document content as bytes
            pages_per_chunk: Number of pages per chunk (default: 2 for Document Intelligence free tier)

        Returns:
            List of tuples containing (chunk_bytes, start_page, end_page)
            where start_page and end_page are 1-based page numbers

        Raises:
            Exception: If PDF processing fails
        """
        try:
            logger.info(f'Splitting PDF into chunks of {pages_per_chunk} pages each')
            
            # Read the PDF from bytes
            pdf_reader = PdfReader(io.BytesIO(pdf_bytes))
            total_pages = len(pdf_reader.pages)
            
            logger.info(f'PDF has {total_pages} total pages')
            
            if total_pages <= pages_per_chunk:
                # If document has fewer pages than chunk size, return as single chunk
                logger.info(f'Document has {total_pages} pages, returning as single chunk')
                return [(pdf_bytes, 1, total_pages)]
            
            chunks = []
            
            # Split into chunks
            for start_page in range(0, total_pages, pages_per_chunk):
                end_page = min(start_page + pages_per_chunk - 1, total_pages - 1)
                
                # Create a new PDF writer for this chunk
                pdf_writer = PdfWriter()
                
                # Add pages to the chunk
                for page_num in range(start_page, end_page + 1):
                    pdf_writer.add_page(pdf_reader.pages[page_num])
                
                # Write chunk to bytes
                chunk_buffer = io.BytesIO()
                pdf_writer.write(chunk_buffer)
                chunk_bytes = chunk_buffer.getvalue()
                chunk_buffer.close()
                
                # Convert to 1-based page numbers for logging/metadata
                start_page_1based = start_page + 1
                end_page_1based = end_page + 1
                
                logger.info(f'Created chunk with pages {start_page_1based}-{end_page_1based} ({len(chunk_bytes)} bytes)')
                chunks.append((chunk_bytes, start_page_1based, end_page_1based))
            
            logger.info(f'Successfully split PDF into {len(chunks)} chunks')
            return chunks
            
        except Exception as e:
            logger.exception(f'Error splitting PDF into chunks: {str(e)}')
            raise

    def get_pdf_page_count(self, pdf_bytes: bytes) -> int:
        """
        Get the total number of pages in a PDF document.

        Args:
            pdf_bytes: PDF document content as bytes

        Returns:
            Number of pages in the PDF

        Raises:
            Exception: If PDF processing fails
        """
        try:
            pdf_reader = PdfReader(io.BytesIO(pdf_bytes))
            page_count = len(pdf_reader.pages)
            logger.info(f'PDF contains {page_count} pages')
            return page_count
        except Exception as e:
            logger.exception(f'Error getting PDF page count: {str(e)}')
            raise

    def is_pdf_document(self, document_bytes: bytes) -> bool:
        """
        Check if the document is a PDF by examining the file header.

        Args:
            document_bytes: Document content as bytes

        Returns:
            True if the document is a PDF, False otherwise
        """
        try:
            # Check PDF magic number
            if document_bytes.startswith(b'%PDF-'):
                return True
            return False
        except Exception:
            logger.exception('Error checking if document is PDF')
            return False
