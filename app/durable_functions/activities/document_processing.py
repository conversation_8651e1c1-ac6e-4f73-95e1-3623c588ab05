from datetime import datetime, timezone
import json
import logging
import os
import time
from typing import Any, Dict, cast
from uuid import UUID

import azure.durable_functions as df
from openai import AsyncAzureOpenAI
from sqlalchemy.ext.asyncio import AsyncSession

from constants.message import MessageRole
from durable_functions.application.config import settings
from durable_functions.application.db import async_session_local
from durable_functions.repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentQueueRepository,
    ExtractedDataRepository,
    OpenAIRepository,
    ProcessingMessageRepository,
)
from durable_functions.utils import (
    ActivityName,
    BlobStorageHelper,
    DocumentIntelligenceHelper,
    ExctractStatus,
    ExtractedDataMerger,
    OrchestratorInputType,
    PDFChunkingHelper,
    RecursiveChunkingStrategy,
    SignalRApiClient,
)
from durable_functions.utils.extracted_data_helpers import format_extracted_data_message, generate_options
from durable_functions.utils.models import FinalExtractionDataResults, LLMExtractedDataResult
from schemas import ExtractedData, ProcessingStatusUpdatePayload

from .models import (
    ChunkDocumentActivityInput,
    ChunkDocumentActivityOutput,
    ExtractDataActivityInput,
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    ReadPromptActivityInput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SendQueueMessageActivityInput,
    UpdateProcessingStatusActivityInput,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()


@bp.activity_trigger('document', ActivityName.ExtractDocumentText)
def extract_document_text(
    document: ExtractDocumentTextActivityInput,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """
    Activity function to extract text from a document using Document Intelligence.

    This function handles the Azure Document Intelligence free tier limitation of only processing
    the first 2 pages by implementing:
    1. PDF chunking into 2-page segments
    2. Sequential processing with rate limiting (1 second delay between API calls)
    3. Result aggregation from all chunks
    """
    try:
        file_name = document.file_name
        logger.info(f'Extracting text from document {file_name} for message {document.message_id}')

        blob_helper = BlobStorageHelper()
        document_bytes = blob_helper.get_blob_from_url(document.blob_url)

        # Check if document is a PDF and needs chunking
        pdf_chunking_helper = PDFChunkingHelper()

        if pdf_chunking_helper.is_pdf_document(document_bytes):
            logger.info(f'Document {file_name} is a PDF, checking if chunking is needed')

            try:
                page_count = pdf_chunking_helper.get_pdf_page_count(document_bytes)
                logger.info(f'PDF has {page_count} pages')

                if page_count > 2:
                    # Document needs chunking due to free tier limitation
                    logger.info(f'PDF has {page_count} pages, splitting into 2-page chunks for free tier processing')
                    return _extract_text_with_chunking(document, document_bytes, blob_helper, pdf_chunking_helper)
                else:
                    logger.info(f'PDF has {page_count} pages, processing as single document')
            except Exception as e:
                logger.warning(f'Failed to get PDF page count, processing as single document: {str(e)}')

        # Process as single document (non-PDF or PDF with ≤2 pages)
        return _extract_text_single_document(document, document_bytes, blob_helper)

    except Exception:
        logger.exception('Error extracting document text')
        raise


def _extract_text_single_document(
    document: ExtractDocumentTextActivityInput,
    document_bytes: bytes,
    blob_helper: BlobStorageHelper,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """Extract text from a single document without chunking."""
    try:
        doc_intelligence = DocumentIntelligenceHelper()
        extraction_result = doc_intelligence.extract_text_from_document(document_bytes)

        base_name = os.path.splitext(document.file_name)[0]  # type: ignore
        extract_filename = f'{base_name}.json'
        extraction_path = f'extracted/{document.message_id}/{extract_filename}'
        extraction_url = blob_helper.upload_json(extraction_path, extraction_result)

        return ExtractDocumentTextActivityOutput(
            message_id=document.message_id,
            file_name=document.file_name,
            extraction_url=extraction_url,
            text_content=extraction_result['text'],
            metadata={
                **extraction_result['metadata'],
                'original_blob_url': document.blob_url,
            },
            status=ExctractStatus.Success,
        )
    except Exception as e:
        logger.error(f'Document Intelligence extraction failed: {str(e)}')
        return ExtractDocumentTextActivityOutputFailed(
            message_id=document.message_id,
            file_name=document.file_name,
            error=f'Document extraction failed: {str(e)}',
            status=ExctractStatus.Failed,
        )


def _extract_text_with_chunking(
    document: ExtractDocumentTextActivityInput,
    document_bytes: bytes,
    blob_helper: BlobStorageHelper,
    pdf_chunking_helper: PDFChunkingHelper,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """Extract text from a PDF document using chunking strategy for free tier compliance."""
    try:
        # Split PDF into 2-page chunks
        chunks = pdf_chunking_helper.split_pdf_into_chunks(document_bytes, pages_per_chunk=2)
        logger.info(f'Split PDF into {len(chunks)} chunks for processing')

        doc_intelligence = DocumentIntelligenceHelper()
        chunk_results = []
        failed_chunks = []

        # Process each chunk sequentially with rate limiting
        for i, (chunk_bytes, start_page, end_page) in enumerate(chunks):
            chunk_num = i + 1
            logger.info(f'Processing chunk {chunk_num}/{len(chunks)} (pages {start_page}-{end_page})')

            try:
                # Extract text from this chunk
                chunk_result = doc_intelligence.extract_text_from_document(chunk_bytes)

                # Add chunk metadata
                chunk_result['chunk_metadata'] = {
                    'chunk_number': chunk_num,
                    'total_chunks': len(chunks),
                    'start_page': start_page,
                    'end_page': end_page,
                    'pages_in_chunk': end_page - start_page + 1,
                }

                chunk_results.append(chunk_result)
                logger.info(f'Successfully processed chunk {chunk_num} (pages {start_page}-{end_page})')

            except Exception as e:
                logger.error(f'Failed to process chunk {chunk_num} (pages {start_page}-{end_page}): {str(e)}')
                failed_chunks.append({
                    'chunk_number': chunk_num,
                    'start_page': start_page,
                    'end_page': end_page,
                    'error': str(e),
                })
                # Continue processing other chunks

            # Rate limiting: 1-second delay between API calls (free tier limit)
            if chunk_num < len(chunks):  # Don't sleep after the last chunk
                logger.info('Applying 1-second rate limit delay before next chunk')
                time.sleep(1)

        if not chunk_results:
            # All chunks failed
            error_msg = f'All {len(chunks)} chunks failed to process'
            logger.error(error_msg)
            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=document.file_name,
                error=error_msg,
                status=ExctractStatus.Failed,
            )

        # Aggregate results from all successful chunks
        consolidated_result = _consolidate_chunk_results(chunk_results, failed_chunks)

        # Store consolidated result
        base_name = os.path.splitext(document.file_name)[0]  # type: ignore
        extract_filename = f'{base_name}.json'
        extraction_path = f'extracted/{document.message_id}/{extract_filename}'
        extraction_url = blob_helper.upload_json(extraction_path, consolidated_result)

        logger.info(f'Successfully processed {len(chunk_results)}/{len(chunks)} chunks')
        if failed_chunks:
            logger.warning(f'{len(failed_chunks)} chunks failed: {failed_chunks}')

        return ExtractDocumentTextActivityOutput(
            message_id=document.message_id,
            file_name=document.file_name,
            extraction_url=extraction_url,
            text_content=consolidated_result['text'],
            metadata={
                **consolidated_result['metadata'],
                'original_blob_url': document.blob_url,
                'chunking_applied': True,
                'failed_chunks': failed_chunks,
            },
            status=ExctractStatus.Success,
        )

    except Exception as e:
        logger.exception(f'Error in chunked extraction: {str(e)}')
        return ExtractDocumentTextActivityOutputFailed(
            message_id=document.message_id,
            file_name=document.file_name,
            error=f'Chunked extraction failed: {str(e)}',
            status=ExctractStatus.Failed,
        )


def _consolidate_chunk_results(chunk_results: list, failed_chunks: list) -> dict:
    """Consolidate extraction results from multiple chunks into a single result."""
    if not chunk_results:
        raise ValueError('No chunk results to consolidate')

    # Combine text from all chunks
    combined_text = ''
    combined_pages = []
    total_page_count = 0
    languages = set()
    key_value_pairs = []

    for result in chunk_results:
        # Combine text content
        if result.get('text'):
            combined_text += result['text'] + '\n\n'

        # Combine metadata
        metadata = result.get('metadata', {})
        chunk_metadata = result.get('chunk_metadata', {})

        # Track page information
        if 'raw_analysis' in result and 'pages' in result['raw_analysis']:
            pages = result['raw_analysis']['pages']
            # Adjust page numbers based on chunk position
            start_page = chunk_metadata.get('start_page', 1)
            for page in pages:
                adjusted_page = dict(page)
                # Adjust page number to reflect position in original document
                if 'page_number' in adjusted_page:
                    adjusted_page['page_number'] = start_page + adjusted_page['page_number'] - 1
                combined_pages.append(adjusted_page)

        # Combine other metadata
        if metadata.get('page_count'):
            total_page_count += metadata['page_count']

        if metadata.get('languages'):
            languages.update(metadata['languages'])

        if metadata.get('key_value_pairs'):
            key_value_pairs.extend(metadata['key_value_pairs'])

    # Clean up combined text
    combined_text = combined_text.strip()

    # Create consolidated result
    consolidated_result = {
        'text': combined_text,
        'metadata': {
            'page_count': total_page_count,
            'document_type': chunk_results[0].get('metadata', {}).get('document_type'),
            'languages': list(languages),
            'key_value_pairs': key_value_pairs,
            'chunking_info': {
                'total_chunks_processed': len(chunk_results),
                'failed_chunks_count': len(failed_chunks),
                'chunk_details': [result.get('chunk_metadata', {}) for result in chunk_results],
            },
        },
        'raw_analysis': {
            'api_version': chunk_results[0].get('raw_analysis', {}).get('api_version'),
            'content_type': chunk_results[0].get('raw_analysis', {}).get('content_type'),
            'pages': combined_pages,
        },
    }

    return consolidated_result


@bp.activity_trigger('updates', ActivityName.UpdateProcessingStatus)
async def update_processing_status(updates: UpdateProcessingStatusActivityInput) -> Dict[str, Any]:
    """
    Activity function to update the file processing status.

    This function demonstrates how to integrate with FastAPI repositories.
    Currently logs the status update, but can be enhanced to use database repositories.
    """
    try:
        logger.info(f'Updating processing status for message_id {updates.message_id}: {updates.status}')

        async with cast(AsyncSession, async_session_local()) as session:
            process_message_repo = ProcessingMessageRepository(session)

            # Update message processing status in database
            await process_message_repo.create(
                data=ProcessingStatusUpdatePayload(
                    message_id=UUID(updates.message_id),
                    status=updates.status,
                    message=updates.message,
                    metadata=updates.metadata,
                )
            )
            await session.commit()

        logger.info(f'Processing status update: {updates.status} - {updates.message}')
        if updates.metadata:
            logger.info(f'Metadata: {updates.metadata}')

        return {
            'status': 'success',
            'message_id': updates.message_id,
            'processing_status': updates.status,
            'message': updates.message,
            'metadata': updates.metadata,
        }

    except Exception:
        logger.exception('Error updating processing status')
        raise


@bp.activity_trigger('notification', ActivityName.SendNotification)
async def send_notification(notification: SendNotificationActivityInput) -> None:
    """
    Activity function to send a notification via SignalR.

    Args:
        notification: Notification data including event type, data, and optional signalr_user_id

    Returns:
        None
    """
    try:
        logger.info(f'Sending notification event: {notification.event_type}')
        signalr_client = SignalRApiClient()

        # Pass the signalr_user_id to the send_notification method
        await signalr_client.send_notification(
            event_type=notification.event_type, data=notification.data, user_id=notification.signalr_user_id
        )

    except Exception:
        logger.exception('Error sending notification')
        raise


@bp.activity_trigger('extraction', ActivityName.ChunkDocument)
def chunk_document(extraction: ChunkDocumentActivityInput) -> ChunkDocumentActivityOutput:
    """
    Activity function to chunk a document.
    """
    try:
        message_id = extraction.message_id
        file_name = extraction.file_name
        text_content = extraction.text_content
        metadata = extraction.metadata

        logger.info(f'Chunking document {file_name} for message {message_id}')

        chunking_strategy = RecursiveChunkingStrategy()
        document_metadata = {
            'message_id': message_id,
            'file_name': file_name,
        }
        for key, value in metadata.items():
            document_metadata[key] = value

        chunks = chunking_strategy.chunk_document(text_content, document_metadata)

        blob_helper = BlobStorageHelper()
        chunk_urls = []

        for chunk in chunks:
            base_name = os.path.splitext(file_name)[0]  # type: ignore
            chunk_filename = f'{base_name}_chunk_{chunk["chunk_index"]}.json'
            chunk_path = f'chunks/{message_id}/{chunk_filename}'
            chunk_url = blob_helper.upload_json(chunk_path, chunk)
            chunk_urls.append({'chunk_index': chunk['chunk_index'], 'chunk_id': chunk['chunk_id'], 'url': chunk_url})

        return ChunkDocumentActivityOutput(
            message_id=message_id,
            file_name=file_name,
            chunk_count=len(chunks),
            chunk_urls=chunk_urls,
        )

    except Exception:
        logger.exception('Error chunking document')
        raise


@bp.activity_trigger('message', ActivityName.SendQueueMessage)
async def send_queue_message(message: SendQueueMessageActivityInput) -> None:
    if message.input_type == OrchestratorInputType.Prompt:
        queue_name = settings.QUEUE_SETTINGS.PROMPT_PROCESSING_QUEUE_CHUNKED
    else:
        queue_name = settings.QUEUE_SETTINGS.DOCUMENT_PROCESSING_QUEUE_CHUNKED

    try:
        logger.info(f'Sending queue message: {message}')

        queue_repo = DocumentQueueRepository(
            connection_string=settings.QUEUE_SETTINGS.CONNECTION_STRING,
            queue_name=queue_name,
        )
        await queue_repo.send_message(message.model_dump())
    except Exception as e:
        logger.error(f'Error sending notification: {str(e)}')
        raise


@bp.activity_trigger('prompt', ActivityName.ReadPrompt)
async def read_prompt(prompt: ReadPromptActivityInput) -> str:
    try:
        prompt_url = prompt.prompt_url
        logger.info(f'Reading prompt from {prompt_url}')
        blob_helper = BlobStorageHelper()
        prompt_text = blob_helper.get_blob_from_url(prompt_url).decode('utf-8')
        return prompt_text
    except Exception:
        logger.exception('Error reading prompt')
        raise


@bp.activity_trigger('extraction', ActivityName.ExtractData)
async def extract_data(extraction: ExtractDataActivityInput) -> LLMExtractedDataResult:
    """
    Activity function to extract data from a document.
    """
    blob_helper = BlobStorageHelper()

    async with AsyncAzureOpenAI(
        azure_endpoint=settings.openai.endpoint,
        api_key=settings.openai.key,
        api_version=settings.openai.api_version,
    ) as client:
        openai_repo = OpenAIRepository(client=client)

        try:
            if extraction.chunk_url:
                chunk_url = extraction.chunk_url
                # Use get_blob_from_url which handles full URLs and extracts the path correctly
                chunk_data_bytes = blob_helper.get_blob_from_url(str(chunk_url))
                chunk_data = json.loads(chunk_data_bytes.decode('utf-8'))
                text = chunk_data.get('text')
            elif extraction.text_content:
                text = extraction.text_content
            else:
                raise ValueError('No chunk_url or text_content provided')

            if text and (stripped_text := text.strip()):
                logger.info('Extracting data from text')
                return await openai_repo.extract_data(stripped_text)
            else:
                return LLMExtractedDataResult()
        except Exception:
            logger.exception('Error extracting data')
            raise


@bp.activity_trigger('merging', ActivityName.MergeExtractionData)
async def merge_extraction_data(merging) -> FinalExtractionDataResults:
    try:
        return ExtractedDataMerger.merge_results(merging)
    except Exception:
        logger.exception('Error merging extraction data')
        raise


@bp.activity_trigger('saving', ActivityName.SaveExtractionData)
async def save_extraction_data(saving: SaveExtractionDataActivityInput):
    """
    Activity function to save the extraction data to the database.
    """
    logger.info(f'Saving extraction data: {saving}')
    async with cast(AsyncSession, async_session_local()) as session:
        extracted_data_repo = ExtractedDataRepository(session, ConversationRepository(session))
        message_repo = ConversationMessageRepository(session, ConversationRepository(session))

        message_id = UUID(saving.message_id)
        logger.info(f'Message ID: {message_id}')
        message = await message_repo.get(message_id)
        if not message:
            logger.warning(f'Message not found for message_id: {message_id}')
            raise ValueError(f'Message not found for message_id: {message_id}')

        client_name = json.dumps(saving.extraction_data.client_names) if saving.extraction_data.client_names else '[]'
        ldmf_country = (
            json.dumps(saving.extraction_data.lead_member_countries)
            if saving.extraction_data.lead_member_countries
            else '[]'
        )
        if saving.extraction_data.periods:
            # get first period
            start_date = saving.extraction_data.periods[0].start_date
            end_date = saving.extraction_data.periods[0].end_date
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
        else:
            start_date = None
            end_date = None

        extracted_data = ExtractedData(
            ConversationPublicId=message.conversation_id,
            DataSourceType=saving.data_source_type,
            ClientName=client_name,
            LDMFCountry=ldmf_country,
            StartDate=start_date,
            EndDate=end_date,
            ObjectiveAndScope=saving.extraction_data.objective_and_scope,
            Outcomes=saving.extraction_data.outcomes,
            CreatedAt=datetime.now(timezone.utc),
        )
        await extracted_data_repo.update(extracted_data)

        # Update the system message content with extracted data information
        try:
            # Get the last message from the conversation (should be a system message)
            last_message = await message_repo.get_last(message.conversation_id)
            logger.info(f'Retrieved last message: {last_message.id}, role: {last_message.role}')
            if last_message.role != MessageRole.SYSTEM:
                logger.warning(f'Last message is not a system message: {last_message.id}')
                await session.commit()
                return

            # Format the extracted data into a user-friendly message
            formatted_content = format_extracted_data_message(saving.extraction_data)
            extracted_options = generate_options(saving.extraction_data)
            # Update the system message content
            updates = {
                'Content': formatted_content,
                'Options': json.dumps(extracted_options),
            }
            await message_repo.update_fields(public_id=last_message.id, updates=updates)
            logger.info(f'Updated system message content for message: {last_message.id}')

        except Exception as e:
            logger.warning(f'Failed to update system message content: {str(e)}')
            # Don't fail the entire operation if message update fails

        await session.commit()
